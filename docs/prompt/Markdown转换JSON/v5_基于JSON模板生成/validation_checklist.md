# JSON转换结果验证清单

## 自动化验证项目

### 1. JSON格式验证
```javascript
// 基础JSON格式检查
function validateJSONFormat(jsonString) {
  try {
    const data = JSON.parse(jsonString);
    return { valid: true, data: data };
  } catch (error) {
    return { valid: false, error: error.message };
  }
}
```

### 2. 必需字段验证
```javascript
// DocumentData必需字段检查
function validateRequiredFields(data) {
  const errors = [];
  
  // 检查根级别必需字段
  if (!data.type) errors.push("缺少type字段");
  if (!data.title) errors.push("缺少title字段");
  if (!Array.isArray(data.widgets)) errors.push("widgets必须为数组");
  
  // 检查每个widget的必需字段
  data.widgets.forEach((widget, index) => {
    if (!widget.serial) errors.push(`Widget[${index}]缺少serial字段`);
    if (!widget.type) errors.push(`Widget[${index}]缺少type字段`);
    
    // 根据控件类型检查特定必需字段
    switch (widget.type) {
      case 'TITLE':
        if (!widget.title) errors.push(`TITLE控件[${index}]缺少title字段`);
        break;
      case 'TEXT':
        if (!widget.content) errors.push(`TEXT控件[${index}]缺少content字段`);
        break;
      case 'LIST':
        if (!Array.isArray(widget.content)) errors.push(`LIST控件[${index}]content必须为数组`);
        break;
      case 'TABLE':
        if (!Array.isArray(widget.cols)) errors.push(`TABLE控件[${index}]缺少cols字段`);
        if (!Array.isArray(widget.content)) errors.push(`TABLE控件[${index}]content必须为数组`);
        break;
      case 'CHART':
        if (!Array.isArray(widget.content)) errors.push(`CHART控件[${index}]content必须为数组`);
        if (['BAR', 'LINE'].includes(widget.style) && !Array.isArray(widget.cols)) {
          errors.push(`${widget.style}图表[${index}]缺少cols字段`);
        }
        break;
      case 'CARD':
        if (!widget.name) errors.push(`CARD控件[${index}]缺少name字段`);
        if (!widget.fields) errors.push(`CARD控件[${index}]缺少fields字段`);
        break;
    }
  });
  
  return errors;
}
```

### 3. 序列编号验证
```javascript
// 序列编号连续性和格式检查
function validateSerialNumbers(widgets) {
  const errors = [];
  const serials = widgets.map(w => w.serial);
  
  // 检查文档标题
  if (!serials.includes("0")) {
    errors.push("缺少文档标题(serial='0')");
  }
  
  // 检查编号格式
  const serialPattern = /^(\d+(\.\d+)*)$/;
  serials.forEach(serial => {
    if (!serialPattern.test(serial)) {
      errors.push(`序列编号格式错误: ${serial}`);
    }
  });
  
  // 检查同级编号连续性
  const levelGroups = {};
  serials.forEach(serial => {
    const parts = serial.split('.');
    const level = parts.length;
    const prefix = parts.slice(0, -1).join('.');
    const key = `${level}-${prefix}`;
    
    if (!levelGroups[key]) levelGroups[key] = [];
    levelGroups[key].push(parseInt(parts[parts.length - 1]));
  });
  
  Object.entries(levelGroups).forEach(([key, numbers]) => {
    numbers.sort((a, b) => a - b);
    for (let i = 0; i < numbers.length - 1; i++) {
      if (numbers[i + 1] - numbers[i] !== 1) {
        errors.push(`序列编号不连续: ${key} - ${numbers}`);
      }
    }
  });
  
  return errors;
}
```

### 4. 数据类型验证
```javascript
// 数据类型正确性检查
function validateDataTypes(widgets) {
  const errors = [];
  
  widgets.forEach((widget, index) => {
    // 检查CHART控件的数值类型
    if (widget.type === 'CHART') {
      widget.content.forEach((series, seriesIndex) => {
        if (widget.style === 'PIE') {
          if (typeof series.content !== 'number') {
            errors.push(`PIE图[${index}]系列[${seriesIndex}]content必须为数字`);
          }
        } else {
          if (!Array.isArray(series.content)) {
            errors.push(`${widget.style}图[${index}]系列[${seriesIndex}]content必须为数组`);
          } else {
            series.content.forEach((value, valueIndex) => {
              if (value !== null && typeof value !== 'number') {
                errors.push(`图表[${index}]数值[${valueIndex}]必须为数字或null`);
              }
            });
          }
        }
      });
    }
    
    // 检查TABLE控件的单元格类型
    if (widget.type === 'TABLE') {
      widget.content.forEach((row, rowIndex) => {
        row.forEach((cell, cellIndex) => {
          if (!cell.type || !cell.content) {
            errors.push(`表格[${index}]单元格[${rowIndex},${cellIndex}]格式错误`);
          }
        });
      });
    }
  });
  
  return errors;
}
```

## 手动验证清单

### 内容准确性检查
- [ ] 所有文本内容与原Markdown一致
- [ ] 数值数据准确无误
- [ ] 表格数据完整对应
- [ ] 图表数据正确转换

### 结构合理性检查
- [ ] 章节层级关系正确
- [ ] 控件类型选择合适
- [ ] 样式属性使用恰当
- [ ] 序列编号逻辑清晰

### 格式规范性检查
- [ ] JSON格式完全有效
- [ ] 字段命名符合规范
- [ ] 枚举值使用正确
- [ ] 特殊字符正确转义

### 完整性验证
- [ ] 没有遗漏重要信息
- [ ] 没有添加虚构内容
- [ ] 章节结构完整
- [ ] 数据关联正确

## 质量评分标准

### A级 (90-100分)
- JSON格式完全正确
- 所有必需字段完整
- 序列编号完全规范
- 内容100%准确
- 结构选择最优

### B级 (80-89分)
- JSON格式正确
- 必需字段基本完整
- 序列编号基本规范
- 内容95%以上准确
- 结构选择合理

### C级 (70-79分)
- JSON格式基本正确
- 必需字段大部分完整
- 序列编号有小问题
- 内容90%以上准确
- 结构选择可接受

### D级 (60-69分)
- JSON格式有问题
- 必需字段缺失较多
- 序列编号混乱
- 内容准确性不足
- 结构选择不当

### F级 (60分以下)
- JSON格式严重错误
- 大量必需字段缺失
- 序列编号完全错误
- 内容严重不准确
- 结构完全不合理

## 常见错误及修复

### 1. 序列编号错误
**错误示例**：`"1", "3", "1.1"` (跳跃编号)
**修复方法**：重新分配连续编号 `"1", "2", "2.1"`

### 2. 数据类型错误
**错误示例**：`"content": "123"` (字符串数值)
**修复方法**：转换为数字类型 `"content": 123`

### 3. 必需字段缺失
**错误示例**：TABLE控件缺少cols字段
**修复方法**：从表格标题行提取列名

### 4. 控件类型选择不当
**错误示例**：表格数据使用TEXT控件
**修复方法**：改用TABLE或CHART控件

## 使用建议

1. **分步验证**：按照清单逐项检查，不要跳过任何步骤
2. **重点关注**：特别注意数据准确性和JSON格式正确性
3. **工具辅助**：使用JSON验证工具进行格式检查
4. **人工复核**：自动化验证后进行人工抽查
5. **持续改进**：记录常见错误，优化转换规则
